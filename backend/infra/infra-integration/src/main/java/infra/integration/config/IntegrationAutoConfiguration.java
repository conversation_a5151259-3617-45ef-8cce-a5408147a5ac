package infra.integration.config;

import com.zaxxer.hikari.HikariDataSource;
import infra.integration.IntegrationConfigService;
import infra.integration.IntegrationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 集成模块自动配置类
 * 负责创建数据源和配置服务Bean
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(IntegrationConfigProperties.class)
@ConditionalOnProperty(prefix = "app.integration")
public class IntegrationAutoConfiguration {

    /**
     * 创建集成配置服务Bean
     *
     * @param properties 集成配置属性
     * @return 集成配置服务实例
     */
    @Bean
    @ConditionalOnMissingBean
    public IntegrationConfigService infraIntegrationConfigService(IntegrationConfigProperties properties) {
        var config = properties.getIntegration();
        // 创建数据源映射
        Map<String, DataSource> dataSources = createDataSources(config);
        log.info("[配置] 集成配置，共{}个集成配置，共{}个数据源", properties.getApis().size(), dataSources.size());
        return new IntegrationConfigService(dataSources, apiConfigs);
    }

    /**
     * 创建数据源映射
     *
     * @param properties 集成配置属性
     * @return 数据源映射
     */
    private Map<String, DataSource> createDataSources(IntegrationConfigProperties properties) {
        Map<String, DataSource> dataSources = new HashMap<>();

        properties.getIntegration().forEach((apiName, apiConfig) -> {
            if (apiConfig.hasDatasource()) {
                try {
                    DataSource dataSource = createDataSource(apiName, apiConfig.getDatasource(), apiConfig.getPoolConfig());
                    dataSources.put(apiName, dataSource);
                    log.info("[配置] 创建集成数据源 : {}", apiName);
                } catch (Exception e) {
                    throw new IntegrationException("创建集成数据源失败 : {}" + apiName, e);
                }
            }
        });

        return dataSources;
    }

    /**
     * 创建单个数据源
     *
     * @param apiName              API名称
     * @param dataSourceProperties 数据源属性
     * @return 数据源实例
     */
    private DataSource createDataSource(String apiName, DataSourceProperties dataSourceProperties) {
        // 使用HikariCP作为连接池
        HikariDataSource dataSource = dataSourceProperties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();

        // 设置连接池名称
        dataSource.setPoolName("IntegrationPool-" + apiName);

        // 如果没有在配置中指定，重置一些默认的连接池参数到更合理的值
        if (dataSource.getMinimumIdle() == 10) {
            dataSource.setMinimumIdle(1);
        }

        return dataSource;
    }
}