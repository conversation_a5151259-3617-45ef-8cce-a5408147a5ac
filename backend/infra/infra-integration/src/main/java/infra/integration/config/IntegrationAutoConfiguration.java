package infra.integration.config;

import com.zaxxer.hikari.HikariDataSource;
import infra.integration.IntegrationConfigService;
import infra.integration.IntegrationException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.util.Map;
import java.util.stream.Collectors;

@Configuration
@EnableConfigurationProperties(IntegrationConfigProperties.class)
@ConditionalOnProperty(prefix = "app.integration", name = "apis")
public class IntegrationAutoConfiguration {

    @Bean
    public IntegrationConfigService integrationConfigService(IntegrationConfigProperties properties) {
        Map<String, DataSource> dataSources = properties.getApis().entrySet().stream()
                .filter(entry -> entry.getValue().getDatasource() != null && entry.getValue().getDatasource().getUrl() != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            try {
                                return entry.getValue().getDatasource().initializeDataSourceBuilder().type(HikariDataSource.class).build();
                            } catch (Exception e) {
                                throw new IntegrationException("Failed to create datasource for api: " + entry.getKey(), e);
                            }
                        }
                ));

        Map<String, IntegrationConfigProperties.ApiConfig> apiConfigs = properties.getApis();

        return new IntegrationConfigService(dataSources, apiConfigs);
    }
}