package infra.integration.config;

import com.zaxxer.hikari.HikariDataSource;
import infra.integration.IntegrationConfigService;
import infra.integration.IntegrationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

/**
 * 集成模块自动配置类
 * 负责创建数据源和配置服务Bean
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(IntegrationConfigProperties.class)
@ConditionalOnProperty(prefix = "app.integration")
public class IntegrationAutoConfiguration {

    /**
     * 创建集成配置服务Bean
     *
     * @param properties 集成配置属性
     * @return 集成配置服务实例
     */
    @Bean
    @ConditionalOnMissingBean
    public IntegrationConfigService infraIntegrationConfigService(IntegrationConfigProperties properties) {
        // 创建数据源映射
        Map<String, DataSource> dataSources = createDataSources(properties);
        // 获取API配置映射
        Map<String, IntegrationConfigProperties.ApiConfig> apiConfigs = properties.getApis();

        log.info("[配置] 集成配置，共{}个集成配置，共{}个数据源", properties.getApis().size(), dataSources.size());
        return new IntegrationConfigService(dataSources, apiConfigs);
    }

    /**
     * 创建数据源映射
     *
     * @param properties 集成配置属性
     * @return 数据源映射
     */
    private Map<String, DataSource> createDataSources(IntegrationConfigProperties properties) {
        Map<String, DataSource> dataSources = new HashMap<>();

        properties.getApis().forEach((apiName, apiConfig) -> {
            if (apiConfig.hasDatasource()) {
                try {
                    DataSource dataSource = createDataSource(apiName, apiConfig.getDatasource(), apiConfig.getPoolConfig());
                    dataSources.put(apiName, dataSource);
                    log.info("[配置] 创建集成数据源 : {}", apiName);
                } catch (Exception e) {
                    throw new IntegrationException("创建集成数据源失败 : {}" + apiName, e);
                }
            }
        });

        return dataSources;
    }

    /**
     * 创建单个数据源
     *
     * @param apiName              API名称
     * @param dataSourceProperties 数据源属性
     * @param poolConfig          连接池配置
     * @return 数据源实例
     */
    private DataSource createDataSource(String apiName, DataSourceProperties dataSourceProperties,
                                      IntegrationConfigProperties.HikariPoolConfig poolConfig) {
        // 使用HikariCP作为连接池
        HikariDataSource dataSource = dataSourceProperties
                .initializeDataSourceBuilder()
                .type(HikariDataSource.class)
                .build();

        // 应用连接池配置
        applyHikariPoolConfig(dataSource, apiName, poolConfig);

        return dataSource;
    }

    /**
     * 应用HikariCP连接池配置
     */
    private void applyHikariPoolConfig(HikariDataSource dataSource, String apiName,
                                     IntegrationConfigProperties.HikariPoolConfig poolConfig) {
        // 设置默认连接池名称
        String defaultPoolName = "IntegrationPool-" + apiName;

        if (poolConfig != null && poolConfig.hasAnyConfig()) {
            // 应用用户配置
            if (poolConfig.getMaximumPoolSize() != null) {
                dataSource.setMaximumPoolSize(poolConfig.getMaximumPoolSize());
            }
            if (poolConfig.getMinimumIdle() != null) {
                dataSource.setMinimumIdle(poolConfig.getMinimumIdle());
            }
            if (poolConfig.getConnectionTimeout() != null) {
                dataSource.setConnectionTimeout(poolConfig.getConnectionTimeout());
            }
            if (poolConfig.getIdleTimeout() != null) {
                dataSource.setIdleTimeout(poolConfig.getIdleTimeout());
            }
            if (poolConfig.getMaxLifetime() != null) {
                dataSource.setMaxLifetime(poolConfig.getMaxLifetime());
            }
            if (poolConfig.getValidationTimeout() != null) {
                dataSource.setValidationTimeout(poolConfig.getValidationTimeout());
            }
            if (poolConfig.getPoolName() != null) {
                dataSource.setPoolName(poolConfig.getPoolName());
            } else {
                dataSource.setPoolName(defaultPoolName);
            }
            if (poolConfig.getAutoCommit() != null) {
                dataSource.setAutoCommit(poolConfig.getAutoCommit());
            }
            if (poolConfig.getConnectionTestQuery() != null) {
                dataSource.setConnectionTestQuery(poolConfig.getConnectionTestQuery());
            }

            log.debug("应用了自定义连接池配置到数据源: {}", apiName);
        } else {
            // 应用默认配置
            dataSource.setPoolName(defaultPoolName);
            // 设置更合理默认值
            if (dataSource.getMaximumPoolSize() == 10) { // HikariCP默认值
                dataSource.setMaximumPoolSize(5);
            }
            if (dataSource.getMinimumIdle() == 10) { // HikariCP默认值
                dataSource.setMinimumIdle(1);
            }
            if (dataSource.getConnectionTimeout() == 30000) { // HikariCP默认值
                dataSource.setConnectionTimeout(10000); // 10秒
            }
            if (dataSource.getIdleTimeout() == 600000) { // HikariCP默认值
                dataSource.setIdleTimeout(300000); // 5分钟
            }

            log.debug("应用了默认连接池配置到数据源: {}", apiName);
        }
    }
}