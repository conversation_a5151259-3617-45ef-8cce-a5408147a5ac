package infra.integration;

import com.zaxxer.hikari.HikariDataSource;
import infra.core.text.Str;
import infra.integration.config.IntegrationConfigProperties;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;

import javax.sql.DataSource;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 集成配置服务
 * 提供第三方API配置和数据源管理功能
 */
@Slf4j
@AllArgsConstructor
public class IntegrationConfigService implements DisposableBean {
    private final Map<String, DataSource> dataSources;
    private final Map<String, IntegrationConfigProperties.ApiConfig> apiConfigs;

    /**
     * 获取指定API的数据源
     *
     * @param apiName API名称
     * @return 数据源，如果不存在则返回空
     */
    public Optional<DataSource> getDataSource(String apiName) {
        if (Str.isEmpty(apiName)) {
            throw new IllegalArgumentException("参数apiName不能为空");
        }

        DataSource dataSource = dataSources.get(apiName);
        return Optional.ofNullable(dataSource);
    }

    /**
     * 获取指定API的配置值并转换为指定类型
     *
     * @param apiName API名称
     * @param key     配置键
     * @param type    目标类型
     * @param <T>     类型参数
     * @return 配置值，如果不存在则返回空
     */
    public <T> Optional<T> getConfig(String apiName, String key, Class<T> type) {
        if (Str.isEmpty(apiName)) {
            throw new IllegalArgumentException("参数apiName不能为空");
        }

        if (Str.isEmpty(key)) {
            throw new IllegalArgumentException("参数key不能为空");
        }

        try {
            return Optional.ofNullable(apiConfigs.get(apiName))
                    .map(config -> config.getConfigValue(key, type));
        } catch (Exception e) {
            throw new IllegalArgumentException("获取配置值失败：" + e.getMessage());
        }
    }

    /**
     * 获取指定API的配置值（Object类型）
     *
     * @param apiName API名称
     * @param key     配置键
     * @return 配置值，如果不存在则返回空
     */
    public Optional<Object> getConfig(String apiName, String key) {
        return getConfig(apiName, key, Object.class);
    }

    /**
     * 检查指定API是否存在
     *
     * @param apiName API名称
     * @return true如果API存在
     */
    public boolean hasApi(String apiName) {
        return apiName != null && apiConfigs.containsKey(apiName);
    }

    /**
     * 检查指定API是否有数据源配置
     *
     * @param apiName API名称
     * @return true如果API有数据源配置
     */
    public boolean hasDataSource(String apiName) {
        return apiName != null && dataSources.containsKey(apiName);
    }

    /**
     * 获取所有已配置的API名称
     *
     * @return API名称集合
     */
    public Set<String> getApiNames() {
        return apiConfigs.keySet();
    }

    /**
     * 获取所有有数据源的API名称
     *
     * @return 有数据源的API名称集合
     */
    public Set<String> getDataSourceApiNames() {
        return dataSources.keySet();
    }

    /**
     * 获取指定API的所有配置键
     *
     * @param apiName API名称
     * @return 配置键集合，如果API不存在则返回空集合
     */
    public Set<String> getConfigKeys(String apiName) {
        return Optional.ofNullable(apiConfigs.get(apiName))
                .map(config -> config.getConfig().keySet())
                .orElse(Set.of());
    }

    @Override
    public void destroy() {
        AtomicInteger closedCount = new AtomicInteger();
        dataSources.values().forEach(dataSource -> {
            if (dataSource instanceof HikariDataSource hikariDataSource) {
                if (!hikariDataSource.isClosed()) {
                    hikariDataSource.close();
                    closedCount.getAndIncrement();
                }
            }
        });
        dataSources.clear();
        log.info("已销毁集成配置服务，关闭{}个数据连接", closedCount);
    }
}