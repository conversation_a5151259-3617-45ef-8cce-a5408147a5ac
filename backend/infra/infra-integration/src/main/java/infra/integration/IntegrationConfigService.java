package infra.integration;

import infra.integration.config.IntegrationConfigProperties;

import javax.sql.DataSource;
import java.util.Map;
import java.util.Optional;

/**
 * 集成配置服务
 */
public class IntegrationConfigService {

    private final Map<String, DataSource> dataSources;
    private final Map<String, IntegrationConfigProperties.ApiConfig> apiConfigs;

    public Optional<DataSource> getDataSource(String apiName) {
        return Optional.ofNullable(dataSources.get(apiName));
    }

    public <T> Optional<T> getConfig(String apiName, String key, Class<T> type) {
        return Optional.ofNullable(apiConfigs.get(apiName))
                .map(IntegrationConfigProperties.ApiConfig::getConfig)
                .map(config -> config.get(key))
                .map(type::cast);
    }

    public Optional<Object> getConfig(String apiName, String key) {
        return getConfig(apiName, key, Object.class);
    }
}