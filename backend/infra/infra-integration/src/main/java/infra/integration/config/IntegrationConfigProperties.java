package infra.integration.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * 集成配置属性
 * 支持配置格式：
 * app:
 *   integration:
 *     api1:
 *       datasource:
 *         url: ******************************
 *         username: user
 *         password: pass
 *         driver-class-name: com.mysql.cj.jdbc.Driver
 *       xx: 1
 *       yy: 2
 *       list:
 *         - item1
 *         - item2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
@ConfigurationProperties(prefix = "app.integration")
public class IntegrationConfigProperties extends HashMap<String, Object> {

    /**
     * 获取所有API配置
     * 这个方法会动态解析配置并返回ApiConfig对象
     */
    public Map<String, ApiConfig> getApis() {
        Map<String, ApiConfig> apiConfigs = new HashMap<>();

        for (Map.Entry<String, Object> entry : this.entrySet()) {
            String apiName = entry.getKey();
            Object configValue = entry.getValue();

            if (configValue instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> configMap = (Map<String, Object>) configValue;
                ApiConfig apiConfig = parseApiConfig(configMap);
                apiConfigs.put(apiName, apiConfig);
                log.debug("Parsed API config for: {} with {} properties", apiName, configMap.size());
            }
        }

        return apiConfigs;
    }

    /**
     * 解析单个API配置
     */
    private ApiConfig parseApiConfig(Map<String, Object> configMap) {
        ApiConfig apiConfig = new ApiConfig();

        // 处理datasource配置
        if (configMap.containsKey("datasource")) {
            Object datasourceConfig = configMap.get("datasource");
            if (datasourceConfig instanceof Map) {
                DataSourceProperties dsProps = getDataSourceProperties((Map<String, Object>) datasourceConfig);

                apiConfig.setDatasource(dsProps);
            }
        }

        // 其他所有配置项都放入config map（除了datasource）
        Map<String, Object> otherConfigs = new HashMap<>(configMap);
        otherConfigs.remove("datasource");
        apiConfig.getConfig().putAll(otherConfigs);

        return apiConfig;
    }

    private static DataSourceProperties getDataSourceProperties(Map<String, Object> datasourceConfig) {
        DataSourceProperties dsProps = new DataSourceProperties();

        if (datasourceConfig.containsKey("url")) {
            dsProps.setUrl((String) datasourceConfig.get("url"));
        }
        if (datasourceConfig.containsKey("username")) {
            dsProps.setUsername((String) datasourceConfig.get("username"));
        }
        if (datasourceConfig.containsKey("password")) {
            dsProps.setPassword((String) datasourceConfig.get("password"));
        }
        if (datasourceConfig.containsKey("driver-class-name")) {
            dsProps.setDriverClassName((String) datasourceConfig.get("driver-class-name"));
        }
        return dsProps;
    }

    /**
     * 单个API的配置
     */
    @Data
    public static class ApiConfig {

        /**
         * 数据源配置（可选）
         */
        @NestedConfigurationProperty
        private DataSourceProperties datasource;

        /**
         * 其他配置项，支持任意类型的值
         * 可以是基本类型、List、Map等
         */
        private Map<String, Object> config = new HashMap<>();

        /**
         * 获取配置值
         *
         * @param key 配置键
         * @return 配置值
         */
        public Object getConfigValue(String key) {
            return config.get(key);
        }

        /**
         * 获取配置值并转换为指定类型
         *
         * @param key  配置键
         * @param type 目标类型
         * @param <T>  类型参数
         * @return 转换后的配置值
         */
        @SuppressWarnings("unchecked")
        public <T> T getConfigValue(String key, Class<T> type) {
            Object value = config.get(key);
            if (value == null) {
                return null;
            }
            if (type.isInstance(value)) {
                return (T) value;
            }
            throw new IllegalArgumentException(
                    String.format("转换配置类型失败 '%s' 类型 %s - %s",
                            key, value.getClass().getSimpleName(), type.getSimpleName())
            );
        }

        /**
         * 检查是否有数据源配置
         *
         * @return true如果配置了数据源
         */
        public boolean hasDatasource() {
            return datasource != null && datasource.getUrl() != null;
        }
    }
}