package infra.integration.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;

/**
 * 集成配置
 */
@Getter
@ConfigurationProperties(prefix = "app.integration")
public class IntegrationConfigProperties {

    private final Map<String, ApiConfig> apis = new HashMap<>();

    @Getter
    public static class ApiConfig {
        @Setter
        private DataSourceProperties datasource;
        private final Map<String, Object> config = new HashMap<>();

    }
}