package infra.integration.config;

import lombok.Data;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * 集成配置属性
 * 支持配置格式：
 * app:
 *   integration:
 *     api1:
 *       datasource:
 *         url: ******************************
 *          username: user
 *          password: pass
 *          driver-class-name: org.postgresql.Driver
 *        xx: 1
 *        yy: 2
 *        list:
 *          - item1
 *          - item2
 */
@Data
@ConfigurationProperties(prefix = "app.integration")
public class IntegrationConfigProperties {

    /**
     * API配置映射，key为API名称，value为API配置
     */
    private Map<String, ApiConfig> apis = new HashMap<>();

    /**
     * 单个API的配置
     */
    @Data
    public static class ApiConfig {

        /**
         * 数据源配置（可选）
         */
        @NestedConfigurationProperty
        private DataSourceProperties datasource;

        /**
         * 其他配置项，支持任意类型的值
         * 可以是基本类型、List、Map等
         */
        private Map<String, Object> config = new HashMap<>();

        /**
         * 获取配置值
         *
         * @param key 配置键
         * @return 配置值
         */
        public Object getConfigValue(String key) {
            return config.get(key);
        }

        /**
         * 获取配置值并转换为指定类型
         *
         * @param key  配置键
         * @param type 目标类型
         * @param <T>  类型参数
         * @return 转换后的配置值
         */
        @SuppressWarnings("unchecked")
        public <T> T getConfigValue(String key, Class<T> type) {
            Object value = config.get(key);
            if (value == null) {
                return null;
            }
            if (type.isInstance(value)) {
                return (T) value;
            }
            throw new IllegalArgumentException(
                    String.format("转换配置类型失败 '%s' 类型 %s - %s",
                            key, value.getClass().getSimpleName(), type.getSimpleName())
            );
        }

        /**
         * 检查是否有数据源配置
         *
         * @return true如果配置了数据源
         */
        public boolean hasDatasource() {
            return datasource != null && datasource.getUrl() != null;
        }
    }
}