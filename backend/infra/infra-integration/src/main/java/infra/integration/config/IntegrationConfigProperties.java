package infra.integration.config;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

import java.util.HashMap;
import java.util.Map;

/**
 * 集成配置属性
 * 支持配置格式：
 * app:
 * integration:
 * api1:
 * datasource:
 * url: ******************************
 * username: user
 * password: pass
 * driver-class-name: com.mysql.cj.jdbc.Driver
 * xx: 1
 * yy: 2
 * list:
 * - item1
 * - item2
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Slf4j
@ConfigurationProperties(prefix = "app.integration")
public class IntegrationConfigProperties extends HashMap<String, Object> {

    /**
     * 获取所有API配置
     * 动态解析配置并返回ApiConfig对象
     */
    public Map<String, ApiConfig> getApis() {
        Map<String, ApiConfig> apiConfigs = new HashMap<>();

        for (Map.Entry<String, Object> entry : this.entrySet()) {
            String apiName = entry.getKey();
            Object configValue = entry.getValue();

            if (configValue instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> configMap = (Map<String, Object>) configValue;
                ApiConfig apiConfig = parseApiConfig(configMap);
                apiConfigs.put(apiName, apiConfig);
            }
        }

        return apiConfigs;
    }

    /**
     * 解析单个API配置
     */
    private ApiConfig parseApiConfig(Map<String, Object> configMap) {
        ApiConfig apiConfig = new ApiConfig();

        // 处理datasource配置
        if (configMap.containsKey("datasource")) {
            Object datasourceConfig = configMap.get("datasource");
            if (datasourceConfig instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dsConfigMap = (Map<String, Object>) datasourceConfig;
                DataSourceProperties dsProps = getDataSourceProperties(dsConfigMap);
                apiConfig.setDatasource(dsProps);

                // 解析连接池配置
                HikariPoolConfig poolConfig = parseHikariPoolConfig(dsConfigMap);
                apiConfig.setPoolConfig(poolConfig);
            }
        }

        // 其他所有配置项都放入config map（除了datasource）
        Map<String, Object> otherConfigs = new HashMap<>(configMap);
        otherConfigs.remove("datasource");
        apiConfig.getConfig().putAll(otherConfigs);

        return apiConfig;
    }

    /**
     * 解析数据源配置属性
     * 支持完整的数据源配置，包括连接池配置
     */
    private static DataSourceProperties getDataSourceProperties(Map<String, Object> datasourceConfig) {
        DataSourceProperties dsProps = new DataSourceProperties();

        // 基本数据源配置
        setStringProperty(datasourceConfig, "url", dsProps::setUrl);
        setStringProperty(datasourceConfig, "username", dsProps::setUsername);
        setStringProperty(datasourceConfig, "password", dsProps::setPassword);
        setStringProperty(datasourceConfig, "driver-class-name", dsProps::setDriverClassName);
        setStringProperty(datasourceConfig, "driverClassName", dsProps::setDriverClassName); // 兼容驼峰命名

        // JNDI配置
        setStringProperty(datasourceConfig, "jndi-name", dsProps::setJndiName);
        setStringProperty(datasourceConfig, "jndiName", dsProps::setJndiName); // 兼容驼峰命名

        // 连接池类型
        setStringProperty(datasourceConfig, "type", value -> {
            try {
                @SuppressWarnings("unchecked")
                Class<? extends javax.sql.DataSource> typeClass = (Class<? extends javax.sql.DataSource>) Class.forName(value);
                dsProps.setType(typeClass);
            } catch (ClassNotFoundException e) {
                log.warn("无法找到数据源类型: {}", value);
            }
        });

        return dsProps;
    }

    /**
     * 解析HikariCP连接池配置
     */
    private static HikariPoolConfig parseHikariPoolConfig(Map<String, Object> datasourceConfig) {
        HikariPoolConfig poolConfig = new HikariPoolConfig();

        // 连接池大小配置
        setIntegerValue(datasourceConfig, "maximum-pool-size", poolConfig::setMaximumPoolSize);
        setIntegerValue(datasourceConfig, "maximumPoolSize", poolConfig::setMaximumPoolSize);
        setIntegerValue(datasourceConfig, "minimum-idle", poolConfig::setMinimumIdle);
        setIntegerValue(datasourceConfig, "minimumIdle", poolConfig::setMinimumIdle);

        // 超时配置
        setLongValue(datasourceConfig, "connection-timeout", poolConfig::setConnectionTimeout);
        setLongValue(datasourceConfig, "connectionTimeout", poolConfig::setConnectionTimeout);
        setLongValue(datasourceConfig, "idle-timeout", poolConfig::setIdleTimeout);
        setLongValue(datasourceConfig, "idleTimeout", poolConfig::setIdleTimeout);
        setLongValue(datasourceConfig, "max-lifetime", poolConfig::setMaxLifetime);
        setLongValue(datasourceConfig, "maxLifetime", poolConfig::setMaxLifetime);
        setLongValue(datasourceConfig, "validation-timeout", poolConfig::setValidationTimeout);
        setLongValue(datasourceConfig, "validationTimeout", poolConfig::setValidationTimeout);

        // 其他配置
        setStringValue(datasourceConfig, "pool-name", poolConfig::setPoolName);
        setStringValue(datasourceConfig, "poolName", poolConfig::setPoolName);
        setBooleanValue(datasourceConfig, "auto-commit", poolConfig::setAutoCommit);
        setBooleanValue(datasourceConfig, "autoCommit", poolConfig::setAutoCommit);
        setStringValue(datasourceConfig, "connection-test-query", poolConfig::setConnectionTestQuery);
        setStringValue(datasourceConfig, "connectionTestQuery", poolConfig::setConnectionTestQuery);

        return poolConfig;
    }

    // 辅助方法
    private static void setStringValue(Map<String, Object> config, String key, java.util.function.Consumer<String> setter) {
        Object value = config.get(key);
        if (value instanceof String) {
            setter.accept((String) value);
        }
    }

    private static void setIntegerValue(Map<String, Object> config, String key, java.util.function.Consumer<Integer> setter) {
        Object value = config.get(key);
        if (value instanceof Number) {
            setter.accept(((Number) value).intValue());
        } else if (value instanceof String) {
            try {
                setter.accept(Integer.parseInt((String) value));
            } catch (NumberFormatException e) {
                log.warn("无法解析整数配置 {}: {}", key, value);
            }
        }
    }

    private static void setLongValue(Map<String, Object> config, String key, java.util.function.Consumer<Long> setter) {
        Object value = config.get(key);
        if (value instanceof Number) {
            setter.accept(((Number) value).longValue());
        } else if (value instanceof String) {
            try {
                setter.accept(Long.parseLong((String) value));
            } catch (NumberFormatException e) {
                log.warn("无法解析长整数配置 {}: {}", key, value);
            }
        }
    }

    private static void setBooleanValue(Map<String, Object> config, String key, java.util.function.Consumer<Boolean> setter) {
        Object value = config.get(key);
        if (value instanceof Boolean) {
            setter.accept((Boolean) value);
        } else if (value instanceof String) {
            setter.accept(Boolean.parseBoolean((String) value));
        }
    }

    /**
     * 设置字符串属性的辅助方法
     */
    private static void setStringProperty(Map<String, Object> config, String key, java.util.function.Consumer<String> setter) {
        Object value = config.get(key);
        if (value instanceof String) {
            setter.accept((String) value);
        }
    }


    /**
     * 单个API的配置
     */
    @Data
    public static class ApiConfig {

        /**
         * 数据源配置（可选）
         */
        @NestedConfigurationProperty
        private DataSourceProperties datasource;

        /**
         * 连接池配置（可选）
         */
        private HikariPoolConfig poolConfig;

        /**
         * 其他配置项，支持任意类型的值
         * 可以是基本类型、List、Map等
         */
        private Map<String, Object> config = new HashMap<>();

        /**
         * 获取配置值
         *
         * @param key 配置键
         * @return 配置值
         */
        public Object getConfigValue(String key) {
            return config.get(key);
        }

        /**
         * 获取配置值并转换为指定类型
         *
         * @param key  配置键
         * @param type 目标类型
         * @param <T>  类型参数
         * @return 转换后的配置值
         */
        @SuppressWarnings("unchecked")
        public <T> T getConfigValue(String key, Class<T> type) {
            Object value = config.get(key);
            if (value == null) {
                return null;
            }
            if (type.isInstance(value)) {
                return (T) value;
            }
            throw new IllegalArgumentException(
                    String.format("转换配置类型失败 '%s' 类型 %s - %s",
                            key, value.getClass().getSimpleName(), type.getSimpleName())
            );
        }

        /**
         * 检查是否有数据源配置
         *
         * @return true如果配置了数据源
         */
        public boolean hasDatasource() {
            return datasource != null && datasource.getUrl() != null;
        }
    }

    /**
     * HikariCP连接池配置
     */
    @Data
    public static class HikariPoolConfig {

        /**
         * 连接池最大大小
         */
        private Integer maximumPoolSize;

        /**
         * 连接池最小空闲连接数
         */
        private Integer minimumIdle;

        /**
         * 连接超时时间（毫秒）
         */
        private Long connectionTimeout;

        /**
         * 空闲连接超时时间（毫秒）
         */
        private Long idleTimeout;

        /**
         * 连接最大生命周期（毫秒）
         */
        private Long maxLifetime;

        /**
         * 验证超时时间（毫秒）
         */
        private Long validationTimeout;

        /**
         * 连接池名称
         */
        private String poolName;

        /**
         * 是否自动提交
         */
        private Boolean autoCommit;

        /**
         * 连接测试查询
         */
        private String connectionTestQuery;

        /**
         * 检查是否有任何配置
         */
        public boolean hasAnyConfig() {
            return maximumPoolSize != null || minimumIdle != null ||
                    connectionTimeout != null || idleTimeout != null ||
                    maxLifetime != null || validationTimeout != null ||
                    poolName != null || autoCommit != null ||
                    connectionTestQuery != null;
        }
    }
}